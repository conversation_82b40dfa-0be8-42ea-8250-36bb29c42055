// API工具函数

import type {
  BaiduApiResponse,
  BaiduIndexApiResponse,
  ParsedFundDataPoint,
  ParsedIndexDataPoint,
  ApiError,
  CacheItem,
  DataFetchOptions
} from './types';

// 内存缓存
const cache = new Map<string, CacheItem<any>>();

/**
 * 解析百度API返回的基金净值数据
 */
export function parseFundData(response: BaiduApiResponse): ParsedFundDataPoint[] {
  try {
    if (!response.Result || response.Result.length === 0) {
      throw new Error('API返回数据为空');
    }

    const result = response.Result[0];
    const series = result.DisplayData?.resultData?.tplData?.series;

    if (!series || series.length === 0) {
      throw new Error('未找到基金数据');
    }

    // 获取净值曲线数据
    const netValueSeries = series.find(s => s.name === '净值曲线');
    if (!netValueSeries) {
      throw new Error('未找到净值曲线数据');
    }

    // 解析数据字符串
    const dataPoints: ParsedFundDataPoint[] = [];
    const dataEntries = netValueSeries.value.split(';');

    for (const entry of dataEntries) {
      if (!entry.trim()) continue;

      const parts = entry.split(',');
      if (parts.length >= 4) {
        const [dateStr, netValue, dailyChange, accValue] = parts;

        // 转换日期格式 (2025-05-06 -> 2025-05-06)
        const date = formatDate(dateStr);

        dataPoints.push({
          date,
          netAssetValue: parseFloat(netValue),
          dailyChange: dailyChange,
          accumulatedValue: parseFloat(accValue),
        });
      }
    }

    return dataPoints.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  } catch (error) {
    console.error('解析基金数据失败:', error);
    throw new Error(`数据解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 解析百度API返回的业绩走势对比数据
 */
export function parsePerformanceData(response: BaiduApiResponse): {
  fund: { date: string; return: number }[];
  benchmark: { date: string; return: number }[];
  index: { date: string; return: number }[];
} {
  try {
    if (!response.Result || response.Result.length === 0) {
      throw new Error('API返回数据为空');
    }

    const result = response.Result[0];
    const series = result.DisplayData?.resultData?.tplData?.series;

    if (!series || series.length === 0) {
      throw new Error('未找到业绩数据');
    }

    const fundSeries = series.find(s => s.name === '本基金');
    const benchmarkSeries = series.find(s => s.name === '同类平均');
    const indexSeries = series.find(s => s.name === '沪深300');

    const parseSeriesData = (seriesData: any) => {
      if (!seriesData) return [];

      const dataPoints: { date: string; return: number }[] = [];
      const dataEntries = seriesData.value.split(';');

      for (const entry of dataEntries) {
        if (!entry.trim()) continue;

        const parts = entry.split(',');
        if (parts.length >= 2) {
          const [dateStr, returnStr] = parts;
          const returnValue = parseFloat(returnStr.replace('%', ''));

          dataPoints.push({
            date: formatDate(dateStr),
            return: returnValue
          });
        }
      }

      return dataPoints.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    };

    return {
      fund: parseSeriesData(fundSeries),
      benchmark: parseSeriesData(benchmarkSeries),
      index: parseSeriesData(indexSeries)
    };
  } catch (error) {
    console.error('解析业绩数据失败:', error);
    throw new Error(`业绩数据解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 格式化日期字符串
 */
export function formatDate(dateStr: string): string {
  // 如果已经是 YYYY-MM-DD 格式，直接返回
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
    return dateStr;
  }

  // 如果是 MM-DD 格式，添加当前年份
  if (/^\d{2}-\d{2}$/.test(dateStr)) {
    const currentYear = new Date().getFullYear();
    return `${currentYear}-${dateStr}`;
  }

  // 其他格式尝试解析
  const date = new Date(dateStr);
  if (isNaN(date.getTime())) {
    throw new Error(`无效的日期格式: ${dateStr}`);
  }

  return date.toISOString().split('T')[0];
}

/**
 * 解析百度指数API返回的数据
 */
export function parseIndexData(response: BaiduIndexApiResponse): ParsedIndexDataPoint[] {
  try {
    if (response.ResultCode !== 0) {
      throw new Error(`API错误: ${response.ResultCode}`);
    }

    const marketData = response.Result?.newMarketData?.marketData;
    if (!marketData) {
      throw new Error('未找到指数数据');
    }

    const dataPoints: ParsedIndexDataPoint[] = [];
    const dataEntries = marketData.split(';');

    for (const entry of dataEntries) {
      if (!entry.trim()) continue;

      const parts = entry.split(',');
      if (parts.length >= 12) {
        const [
          timestamp, time, open, close, volume, high, low, amount,
          range, ratio, turnoverratio, preClose, ma5avgprice, ma5volume,
          ma10avgprice, ma10volume, ma20avgprice, ma20volume
        ] = parts;

        dataPoints.push({
          timestamp: parseInt(timestamp),
          time,
          open: parseFloat(open),
          close: parseFloat(close),
          volume: parseFloat(volume),
          high: parseFloat(high),
          low: parseFloat(low),
          amount: parseFloat(amount),
          range: parseFloat(range),
          ratio: parseFloat(ratio),
          turnoverratio,
          preClose: parseFloat(preClose),
          ma5avgprice: ma5avgprice && ma5avgprice !== '--' ? parseFloat(ma5avgprice) : undefined,
          ma5volume: ma5volume && ma5volume !== '--' ? parseFloat(ma5volume) : undefined,
          ma10avgprice: ma10avgprice && ma10avgprice !== '--' ? parseFloat(ma10avgprice) : undefined,
          ma10volume: ma10volume && ma10volume !== '--' ? parseFloat(ma10volume) : undefined,
          ma20avgprice: ma20avgprice && ma20avgprice !== '--' ? parseFloat(ma20avgprice) : undefined,
          ma20volume: ma20volume && ma20volume !== '--' ? parseFloat(ma20volume) : undefined,
        });
      }
    }

    return dataPoints.sort((a, b) => a.timestamp - b.timestamp);
  } catch (error) {
    console.error('解析指数数据失败:', error);
    throw new Error(`数据解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 构建百度基金API请求URL
 *
 * @param fundCode 基金代码
 * @param options 请求选项
 * @param options.dataType 数据类型: 'nvl'=净值数据, 'ai'=业绩走势对比
 * @param options.months 数据月数: 1,3,6,12,36,60,124等
 * @param options.source 数据源: 'qieman'=且慢数据源(用于ai类型)
 * @returns 完整的API请求URL
 */
export function buildApiUrl(fundCode: string, options: {
  dataType?: 'nvl' | 'ai';
  months?: number;
  source?: string;
} = {}): string {
  const baseUrl = 'https://gushitong.baidu.com/opendata';
  const params = new URLSearchParams({
    resource_id: '5824',
    query: fundCode,
    new_need_di: '1',
    m: (options.months || 12).toString(),
    t: options.dataType || 'nvl',
    finClientType: 'pc'
  });

  // 对于ai类型的数据，需要添加source参数
  if (options.dataType === 'ai' && options.source) {
    params.set('source', options.source);
  }

  return `${baseUrl}?${params.toString()}`;
}

/**
 * 构建百度指数API请求URL
 */
export function buildIndexApiUrl(indexCode: string, options: {
  ktype?: string;
  count?: number;
  endTime?: string;
} = {}): string {
  const baseUrl = 'https://finance.pae.baidu.com/vapi/v1/getquotation';
  const params = new URLSearchParams({
    srcid: '5353',
    all: '1',
    pointType: 'string',
    group: 'quotation_index_kline',
    query: indexCode,
    code: indexCode,
    market_type: 'ab',
    newFormat: '1',
    name: getIndexName(indexCode),
    is_kc: '0',
    ktype: options.ktype || 'week',
    count: (options.count || 107).toString(),
    finClientType: 'pc'
  });

  if (options.endTime) {
    params.set('end_time', options.endTime);
  }

  return `${baseUrl}?${params.toString()}`;
}

/**
 * 获取指数名称
 */
function getIndexName(indexCode: string): string {
  const indexNames: Record<string, string> = {
    '000001': '上证指数',
    '399001': '深证成指',
    '399006': '创业板指',
    '000300': '沪深300',
    '000905': '中证500',
    '000016': '上证50'
  };

  return encodeURIComponent(indexNames[indexCode] || '指数');
}

/**
 * 验证基金代码格式
 */
export function validateFundCode(code: string): boolean {
  // 基金代码通常是6位数字
  return /^\d{6}$/.test(code);
}

/**
 * 验证API请求参数
 */
export function validateApiParams(params: {
  fundCode: string;
  dataType?: 'nvl' | 'ai';
  months?: number;
  source?: string;
}): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // 验证基金代码
  if (!params.fundCode) {
    errors.push('基金代码不能为空');
  } else if (!validateFundCode(params.fundCode)) {
    errors.push('基金代码格式错误，应为6位数字');
  }

  // 验证数据类型
  if (params.dataType && !['nvl', 'ai'].includes(params.dataType)) {
    errors.push('数据类型只能是 nvl 或 ai');
  }

  // 验证月数参数
  if (params.months !== undefined) {
    const validMonths = [1, 3, 6, 12, 36, 60, 124];
    if (!validMonths.includes(params.months)) {
      errors.push(`月数参数无效，支持的值: ${validMonths.join(', ')}`);
    }
  }

  // 验证ai类型的source参数
  if (params.dataType === 'ai' && !params.source) {
    errors.push('ai类型数据需要指定source参数');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 创建API错误对象
 */
export function createApiError(code: string, message: string, details?: any): ApiError {
  return {
    code,
    message,
    details
  };
}

/**
 * 缓存管理
 */
export class CacheManager {
  private static instance: CacheManager;
  private cache = new Map<string, CacheItem<any>>();

  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  set<T>(key: string, data: T, ttl: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

/**
 * 重试机制
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: { maxRetries?: number; delay?: number } = {}
): Promise<T> {
  const { maxRetries = 3, delay = 1000 } = options;
  let lastError: Error;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      if (i === maxRetries) {
        throw lastError;
      }

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
    }
  }

  throw lastError!;
}

/**
 * 请求超时处理
 */
export function withTimeout<T>(promise: Promise<T>, timeout: number): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('请求超时')), timeout);
    })
  ]);
}

/**
 * 生成缓存键
 */
export function generateCacheKey(fundCode: string, startDate?: string, endDate?: string): string {
  const parts = [fundCode];
  if (startDate) parts.push(startDate);
  if (endDate) parts.push(endDate);
  return parts.join('_');
}

/**
 * 日期范围验证
 */
export function validateDateRange(startDate: string, endDate: string): boolean {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return false;
  }

  return start <= end;
}

/**
 * 格式化错误消息
 */
export function formatErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return '未知错误';
}
