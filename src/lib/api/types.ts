// 百度股市通API相关类型定义

// 百度API原始响应结构
export interface BaiduApiResponse {
  QueryID: string;
  Result: BaiduApiResult[];
  ResultCode: string;
  ResultNum: string;
}

export interface BaiduApiResult {
  ClickNeed: string;
  DisplayData: {
    StdStg: string;
    StdStl: string;
    resultData: {
      extData: {
        OriginQuery: string;
        resourceid: string;
        tplt: string;
      };
      tplData: {
        ResultURL: string;
        card_order: string;
        data_source: string;
        digits: string;
        disp_data_url_ex: {
          aesplitid: string;
        };
        lyAxis: any[];
        maxPoints: string;
        sec: number;
        series: BaiduApiSeries[];
        showDate: string;
        showTag: string;
        text: string;
        xAxis: string[];
      };
    };
    strategy: {
      ctplOrPhp: string;
      hilightWord: string;
      precharge: string;
      tempName: string;
    };
  };
  OriginSrcID: string;
  RecoverCacheTime: string;
  ResultURL: string;
  Sort: string;
  SrcID: string;
  SubResNum: string;
  SubResult: any[];
  Weight: string;
}

export interface BaiduApiSeries {
  label: string[];
  name: string;
  value: string; // 格式: "日期,净值,涨幅,累计净值;..." 或 "日期,收益率;..."
  special?: string; // 特殊标记，用于区分不同类型的数据
}

// 解析后的基金数据点
export interface ParsedFundDataPoint {
  date: string;
  netAssetValue: number;
  dailyChange: string;
  accumulatedValue: number;
}

// 业绩对比数据点
export interface PerformanceDataPoint {
  date: string;
  return: number; // 收益率 (%)
}

// 业绩对比数据集合
export interface PerformanceComparisonData {
  fund: PerformanceDataPoint[]; // 本基金
  benchmark: PerformanceDataPoint[]; // 同类平均
  index: PerformanceDataPoint[]; // 指数(如沪深300)
}

// API请求参数
export interface FundApiParams {
  fundCode: string;
  startDate?: string;
  endDate?: string;
  dataType?: 'nvl' | 'ai'; // nvl=净值数据, ai=业绩走势对比
  months?: number; // 数据月数: 1,3,6,12,36,60,124
  source?: string; // 数据源，ai类型时使用
}

// API错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

// 基金基本信息（从其他API获取）
export interface FundBasicInfo {
  code: string;
  name: string;
  type: string;
  manager: string;
  establishDate: string;
  scale?: string;
  description?: string;
}

// 缓存项结构
export interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

// API响应状态
export type ApiStatus = 'idle' | 'loading' | 'success' | 'error';

// 数据获取选项
export interface DataFetchOptions {
  useCache?: boolean;
  timeout?: number;
  retryCount?: number;
  retryDelay?: number;
}

// 百度指数API响应结构
export interface BaiduIndexApiResponse {
  ResultCode: number;
  ResultNum: number;
  QueryID: string;
  Result: {
    newMarketData: {
      headers: string[];
      keys: string[];
      marketData: string; // 分号分隔的数据字符串
    };
  };
}

// 解析后的指数数据点
export interface ParsedIndexDataPoint {
  timestamp: number;
  time: string;
  open: number;
  close: number;
  volume: number;
  high: number;
  low: number;
  amount: number;
  range: number;
  ratio: number;
  turnoverratio: string;
  preClose: number;
  ma5avgprice?: number;
  ma5volume?: number;
  ma10avgprice?: number;
  ma10volume?: number;
  ma20avgprice?: number;
  ma20volume?: number;
}

// 指数API请求参数
export interface IndexApiParams {
  indexCode: string;
  ktype?: 'day' | 'week' | 'month'; // K线类型
  count?: number; // 数据条数
  endTime?: string; // 结束时间
}
